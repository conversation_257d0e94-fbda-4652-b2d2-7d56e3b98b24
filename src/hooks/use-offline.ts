'use client';

import { errorLogger } from '@/lib/error-handling';
import { useCallback, useEffect, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export interface OfflineState {
	isOnline: boolean;
	isOffline: boolean;
	lastOnlineTime: Date | null;
	lastOfflineTime: Date | null;
	connectionType: string | null;
	effectiveType: string | null;
}

export interface UseOfflineOptions {
	pingUrl?: string;
	pingInterval?: number;
	pingTimeout?: number;
	onOnline?: () => void;
	onOffline?: () => void;
}

// ============================================================================
// OFFLINE DETECTION HOOK
// ============================================================================

export function useOffline(options: UseOfflineOptions = {}) {
	const {
		pingUrl = '/api/health',
		pingInterval = 30000, // 30 seconds
		pingTimeout = 5000, // 5 seconds
		onOnline,
		onOffline,
	} = options;

	const [state, setState] = useState<OfflineState>(() => ({
		isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
		isOffline: typeof navigator !== 'undefined' ? !navigator.onLine : false,
		lastOnlineTime: null,
		lastOfflineTime: null,
		connectionType: null,
		effectiveType: null,
	}));

	// Get connection information
	const getConnectionInfo = useCallback(() => {
		if (typeof navigator === 'undefined') return { connectionType: null, effectiveType: null };

		const connection =
			(navigator as any).connection ||
			(navigator as any).mozConnection ||
			(navigator as any).webkitConnection;

		return {
			connectionType: connection?.type || null,
			effectiveType: connection?.effectiveType || null,
		};
	}, []);

	// Ping server to verify connectivity
	const pingServer = useCallback(async (): Promise<boolean> => {
		try {
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), pingTimeout);

			const response = await fetch(pingUrl, {
				method: 'HEAD',
				signal: controller.signal,
				cache: 'no-cache',
			});

			clearTimeout(timeoutId);
			return response.ok;
		} catch (error) {
			errorLogger.debug('Server ping failed', { error, pingUrl }, 'useOffline');
			return false;
		}
	}, [pingUrl, pingTimeout]);

	// Update online/offline state
	const updateState = useCallback(
		(isOnline: boolean, source: string) => {
			const connectionInfo = getConnectionInfo();
			const now = new Date();

			setState((prevState) => {
				// Only update if state actually changed
				if (prevState.isOnline === isOnline) {
					return {
						...prevState,
						...connectionInfo,
					};
				}

				const newState: OfflineState = {
					isOnline,
					isOffline: !isOnline,
					lastOnlineTime: isOnline ? now : prevState.lastOnlineTime,
					lastOfflineTime: !isOnline ? now : prevState.lastOfflineTime,
					...connectionInfo,
				};

				// Log state change
				errorLogger.info(
					`Network status changed to ${isOnline ? 'online' : 'offline'}`,
					{ source, connectionInfo },
					'useOffline'
				);

				// Call callbacks
				if (isOnline && onOnline) {
					onOnline();
				} else if (!isOnline && onOffline) {
					onOffline();
				}

				return newState;
			});
		},
		[getConnectionInfo, onOnline, onOffline]
	);

	// Handle browser online/offline events
	useEffect(() => {
		if (typeof window === 'undefined') return;

		const handleOnline = () => updateState(true, 'browser_event');
		const handleOffline = () => updateState(false, 'browser_event');

		window.addEventListener('online', handleOnline);
		window.addEventListener('offline', handleOffline);

		// Initial state check
		updateState(navigator.onLine, 'initial');

		return () => {
			window.removeEventListener('online', handleOnline);
			window.removeEventListener('offline', handleOffline);
		};
	}, [updateState]);

	// Periodic server ping
	useEffect(() => {
		if (typeof window === 'undefined' || !state.isOnline) return;

		const interval = setInterval(async () => {
			const serverReachable = await pingServer();
			if (!serverReachable && state.isOnline) {
				updateState(false, 'server_ping');
			} else if (serverReachable && !state.isOnline) {
				updateState(true, 'server_ping');
			}
		}, pingInterval);

		return () => clearInterval(interval);
	}, [pingServer, pingInterval, state.isOnline, updateState]);

	// Manual connectivity check
	const checkConnectivity = useCallback(async (): Promise<boolean> => {
		const browserOnline = navigator.onLine;
		const serverReachable = await pingServer();
		const isOnline = browserOnline && serverReachable;

		updateState(isOnline, 'manual_check');
		return isOnline;
	}, [pingServer, updateState]);

	return {
		...state,
		checkConnectivity,
	};
}

// ============================================================================
// OFFLINE STORAGE UTILITIES
// ============================================================================

export interface OfflineAction {
	id: string;
	type: string;
	payload: any;
	timestamp: Date;
	retryCount: number;
	maxRetries: number;
}

export class OfflineQueue {
	private static instance: OfflineQueue;
	private queue: OfflineAction[] = [];
	private isProcessing = false;

	private constructor() {
		this.loadFromStorage();
	}

	public static getInstance(): OfflineQueue {
		if (!OfflineQueue.instance) {
			OfflineQueue.instance = new OfflineQueue();
		}
		return OfflineQueue.instance;
	}

	public add(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>): void {
		const offlineAction: OfflineAction = {
			...action,
			id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			timestamp: new Date(),
			retryCount: 0,
		};

		this.queue.push(offlineAction);
		this.saveToStorage();

		errorLogger.info(
			'Action queued for offline processing',
			{ actionType: action.type, queueLength: this.queue.length },
			'OfflineQueue'
		);
	}

	public async processQueue(): Promise<void> {
		if (this.isProcessing || this.queue.length === 0) return;

		this.isProcessing = true;
		errorLogger.info(
			'Processing offline queue',
			{ queueLength: this.queue.length },
			'OfflineQueue'
		);

		const processedActions: string[] = [];

		for (const action of this.queue) {
			try {
				await this.processAction(action);
				processedActions.push(action.id);
			} catch (error) {
				action.retryCount++;

				if (action.retryCount >= action.maxRetries) {
					errorLogger.error(
						'Offline action exceeded max retries',
						error instanceof Error ? error : new Error(String(error)),
						{ action },
						'OfflineQueue'
					);
					processedActions.push(action.id); // Remove from queue
				} else {
					errorLogger.warn(
						'Offline action retry failed',
						{ action, error },
						'OfflineQueue'
					);
				}
			}
		}

		// Remove processed actions
		this.queue = this.queue.filter((action) => !processedActions.includes(action.id));
		this.saveToStorage();
		this.isProcessing = false;

		errorLogger.info(
			'Offline queue processing completed',
			{ processed: processedActions.length, remaining: this.queue.length },
			'OfflineQueue'
		);
	}

	public getQueue(): OfflineAction[] {
		return [...this.queue];
	}

	public clear(): void {
		this.queue = [];
		this.saveToStorage();
	}

	private async processAction(action: OfflineAction): Promise<void> {
		// This would be implemented based on your specific action types
		// For example, API calls, data synchronization, etc.
		throw new Error('processAction must be implemented for specific action types');
	}

	private saveToStorage(): void {
		if (typeof localStorage === 'undefined') return;

		try {
			localStorage.setItem('offline_queue', JSON.stringify(this.queue));
		} catch (error) {
			errorLogger.error(
				'Failed to save offline queue to storage',
				error instanceof Error ? error : new Error(String(error)),
				{},
				'OfflineQueue'
			);
		}
	}

	private loadFromStorage(): void {
		if (typeof localStorage === 'undefined') return;

		try {
			const stored = localStorage.getItem('offline_queue');
			if (stored) {
				this.queue = JSON.parse(stored).map((action: any) => ({
					...action,
					timestamp: new Date(action.timestamp),
				}));
			}
		} catch (error) {
			errorLogger.error(
				'Failed to load offline queue from storage',
				error instanceof Error ? error : new Error(String(error)),
				{},
				'OfflineQueue'
			);
			this.queue = [];
		}
	}
}

// ============================================================================
// OFFLINE QUEUE HOOK
// ============================================================================

export function useOfflineQueue() {
	const queue = OfflineQueue.getInstance();
	const [queueLength, setQueueLength] = useState(queue.getQueue().length);

	const addToQueue = useCallback(
		(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>) => {
			queue.add(action);
			setQueueLength(queue.getQueue().length);
		},
		[queue]
	);

	const processQueue = useCallback(async () => {
		await queue.processQueue();
		setQueueLength(queue.getQueue().length);
	}, [queue]);

	const clearQueue = useCallback(() => {
		queue.clear();
		setQueueLength(0);
	}, [queue]);

	return {
		queueLength,
		addToQueue,
		processQueue,
		clearQueue,
		getQueue: queue.getQueue.bind(queue),
	};
}
