'use client';

import { DialogActionButtons, DialogForm, Input } from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { useCallback, useEffect, useState } from 'react';

export function RenameCollectionDialog({
	open,
	onOpenChange,
	collection,
	renameCollection,
	renameLoading,
	renameError,
}: {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	collection: { id: string; name: string };
	renameCollection: (params: { name: string }) => Promise<void>;
	renameLoading: boolean;
	renameError?: { message: string } | null;
}) {
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();
	const [newCollectionName, setNewCollectionName] = useState(collection.name);

	useEffect(() => {
		if (open) setNewCollectionName(collection.name);
	}, [open, collection.name]);

	const handleSubmit = useCallback(async () => {
		if (!newCollectionName.trim() || !collection) return;
		try {
			await renameCollection({ name: newCollectionName.trim() });
			onOpenChange(false);
			showSuccess(
				t('collections.rename_success'),
				t('collections.rename_success_desc', {
					name: newCollectionName.trim(),
				})
			);
		} catch (error: any) {
			showError(
				error instanceof Error ? error : new Error(t('collections.rename_error'))
			);
		}
	}, [newCollectionName, collection, renameCollection, onOpenChange, showSuccess, showError, t, renameError]);

	const handleCancel = useCallback(() => {
		onOpenChange(false);
	}, [onOpenChange]);

	return (
		<DialogForm
			open={open}
			onOpenChange={onOpenChange}
			titleKey="collections.edit"
			onSubmit={(e) => {
				e.preventDefault();
				handleSubmit();
			}}
			footer={
				<DialogActionButtons
					onCancel={handleCancel}
					onSubmit={handleSubmit}
					submitDisabled={!newCollectionName.trim()}
					isLoading={renameLoading}
				/>
			}
		>
			<Input
				value={newCollectionName}
				onChange={(e) => setNewCollectionName(e.target.value)}
				placeholder={t('collections.rename_placeholder')}
				className="bg-background/50 backdrop-blur-sm focus:ring-2 focus:ring-primary/20 transition-all duration-200"
				disabled={renameLoading}
				
			/>
		</DialogForm>
	);
}
